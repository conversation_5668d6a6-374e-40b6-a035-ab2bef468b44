"use client";

import React from "react";
import { motion } from "motion/react";
import { useTableOfContents } from "@/hooks/use-table-of-contents";
import { cn } from "@/lib/utils";
import { FileText } from "lucide-react";

export const TableOfContents: React.FC = () => {
  const { toc, activeId, scrollToHeading } = useTableOfContents();

  // Don't render if there are no headings or only one heading
  if (toc.length <= 1) {
    return null;
  }

  return (
    <aside className="hidden xl:block w-64 h-[calc(100dvh-57px)] sticky top-[57px] flex-shrink-0">
      <div className="h-full py-8 px-6 overflow-y-auto">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center gap-2 text-sm font-semibold text-zinc-900 dark:text-zinc-100">
            <FileText className="w-4 h-4" />
            <span>On this page</span>
          </div>

          {/* Table of Contents */}
          <nav className="space-y-1" role="navigation" aria-label="Table of contents">
            {toc.map((item, index) => {
              const isActive = item.id === activeId;
              
              return (
                <motion.button
                  key={item.id}
                  onClick={() => scrollToHeading(item.id)}
                  className={cn(
                    "w-full text-left px-3 py-2 rounded-md text-sm transition-all duration-200",
                    "hover:bg-zinc-100 dark:hover:bg-zinc-800",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-zinc-950",
                    isActive
                      ? "bg-blue-50 dark:bg-blue-950/20 text-blue-700 dark:text-blue-300 font-medium border-l-2 border-blue-500"
                      : "text-zinc-600 dark:text-zinc-400 border-l-2 border-transparent"
                  )}
                  initial={false}
                  animate={{
                    x: isActive ? 4 : 0,
                  }}
                  transition={{ duration: 0.2 }}
                >
                  <span className="block truncate">
                    {item.text}
                  </span>
                </motion.button>
              );
            })}
          </nav>

          {/* Scroll Progress Indicator */}
          <div className="mt-8 pt-4 border-t border-zinc-200 dark:border-zinc-700">
            <div className="text-xs text-zinc-500 dark:text-zinc-400 mb-2">
              Reading progress
            </div>
            <div className="w-full bg-zinc-200 dark:bg-zinc-700 rounded-full h-1">
              <motion.div
                className="bg-blue-500 h-1 rounded-full"
                initial={{ width: "0%" }}
                animate={{ 
                  width: toc.length > 0 
                    ? `${((toc.findIndex(item => item.id === activeId) + 1) / toc.length) * 100}%`
                    : "0%"
                }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              />
            </div>
            <div className="flex justify-between text-xs text-zinc-500 dark:text-zinc-400 mt-1">
              <span>Start</span>
              <span>End</span>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-6 pt-4 border-t border-zinc-200 dark:border-zinc-700">
            <div className="space-y-2">
              <button
                onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
                className="w-full text-left px-3 py-2 rounded-md text-sm text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors"
              >
                ↑ Back to top
              </button>
              <button
                onClick={() => window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" })}
                className="w-full text-left px-3 py-2 rounded-md text-sm text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors"
              >
                ↓ Go to bottom
              </button>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
};
