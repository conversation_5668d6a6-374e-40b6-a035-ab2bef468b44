"use client";

import React from "react";
import { motion } from "motion/react";
import { useTableOfContents } from "@/hooks/use-table-of-contents";
import { cn } from "@/lib/utils";
import { ArrowUp, ArrowDown } from "lucide-react";

export const TableOfContents: React.FC = () => {
  const { toc, activeId, scrollToHeading } = useTableOfContents();

  // Don't render if there are no headings or only one heading
  if (toc.length <= 1) {
    return null;
  }

  return (
    <aside className="hidden xl:block w-64 h-[calc(100dvh-57px)] sticky top-[57px] flex-shrink-0">
      <div className="h-full py-8 px-6 overflow-y-auto">
        <div className="space-y-6">
          {/* Timeline Table of Contents */}
          <nav className="relative" role="navigation" aria-label="Table of contents">
            {/* Timeline line */}
            <div className="absolute left-2 top-0 bottom-0 w-px bg-zinc-200 dark:bg-zinc-700" />

            <div className="space-y-4">
              {toc.map((item, index) => {
                const isActive = item.id === activeId;

                return (
                  <div key={item.id} className="relative flex items-start">
                    {/* Timeline dot */}
                    <motion.div
                      className={cn(
                        "relative z-10 w-4 h-4 rounded-full border-2 transition-all duration-200",
                        isActive
                          ? "bg-blue-500 border-blue-500 shadow-lg shadow-blue-500/25"
                          : "bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-600"
                      )}
                      animate={{
                        scale: isActive ? 1.2 : 1,
                      }}
                      transition={{ duration: 0.2 }}
                    />

                    {/* Content */}
                    <button
                      onClick={() => scrollToHeading(item.id)}
                      className={cn(
                        "ml-4 text-left text-sm transition-all duration-200 hover:text-zinc-900 dark:hover:text-zinc-100",
                        isActive
                          ? "text-blue-600 dark:text-blue-400 font-medium"
                          : "text-zinc-600 dark:text-zinc-400"
                      )}
                    >
                      {item.text}
                    </button>
                  </div>
                );
              })}
            </div>
          </nav>

          {/* Reading Progress */}
          <div className="space-y-3">
            <div className="text-xs font-medium text-zinc-500 dark:text-zinc-400">
              Reading progress
            </div>
            <div className="w-full bg-zinc-200 dark:bg-zinc-700 rounded-full h-1.5">
              <motion.div
                className="bg-gradient-to-r from-blue-500 to-blue-600 h-1.5 rounded-full shadow-sm"
                initial={{ width: "0%" }}
                animate={{
                  width: toc.length > 0
                    ? `${((toc.findIndex(item => item.id === activeId) + 1) / toc.length) * 100}%`
                    : "0%"
                }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              />
            </div>
            <div className="flex justify-between text-xs text-zinc-500 dark:text-zinc-400">
              <span>Start</span>
              <span>End</span>
            </div>
          </div>

          {/* Glassy Navigation Buttons */}
          <div className="space-y-2">
            <button
              onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
              className="w-full flex items-center gap-2 px-3 py-2 text-sm text-zinc-700 dark:text-zinc-300 bg-white/60 dark:bg-zinc-800/60 backdrop-blur-md border border-white/20 dark:border-zinc-700/50 rounded-lg hover:bg-white/80 dark:hover:bg-zinc-800/80 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <ArrowUp className="w-3.5 h-3.5" />
              <span>Back to top</span>
            </button>
            <button
              onClick={() => window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" })}
              className="w-full flex items-center gap-2 px-3 py-2 text-sm text-zinc-700 dark:text-zinc-300 bg-white/60 dark:bg-zinc-800/60 backdrop-blur-md border border-white/20 dark:border-zinc-700/50 rounded-lg hover:bg-white/80 dark:hover:bg-zinc-800/80 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <ArrowDown className="w-3.5 h-3.5" />
              <span>Go to bottom</span>
            </button>
          </div>
        </div>
      </div>
    </aside>
  );
};
